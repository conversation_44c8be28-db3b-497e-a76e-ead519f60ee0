from pydantic import BaseModel
from pydantic_settings import BaseSettings


class CacheSettings(BaseModel):
    default_expiration: int = 7200


class CorsSettings(BaseModel):
    allow_origins: list = [
        #local development
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        #amplify
        'https://dev.d1zhws5nq4uliu.amplifyapp.com/',  #dev
        'https://stage.d3umavc4ik1yxx.amplifyapp.com', #uat
        'https://uat.verityri.com',                    #uat
        'https://main.d2bhczol2emhg7.amplifyapp.com',  #prod
        'https://client.verityri.com',                 #prod
    ]
    allow_methods: list = [
        'get',
        'post',
        'PATCH'
    ]


class CompressionSettings(BaseModel):
    backend: str = 'brotli'
    brotli_gzip_fallback: bool = True


class Settings(BaseSettings):
    cache: CacheSettings = CacheSettings()
    cors: CorsSettings = CorsSettings()
    compression: CompressionSettings = CompressionSettings()

settings = Settings()