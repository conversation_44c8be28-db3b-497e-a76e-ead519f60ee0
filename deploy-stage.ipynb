#%%
name: Deploy to Stage
run-name: Auto-deploy to stage from ${{ github.ref_name }} branch

on:
  push:
    branches:
      - stage

# Add permissions needed for OIDC
permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: stage

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12.7'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.0

      - name: Install dependencies
        run: |
          poetry config virtualenvs.create false
          poetry install --no-interaction --no-ansi

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-region: ${{ secrets.AWS_REGION }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Build APIs
        run: ./build-apis stage

      - name: Deploy APIs
        uses: pulumi/actions@v6
        with:
          command: up
          stack-name: stage
          work-dir: ./api
          cloud-url: s3://crc-pulumi/crc
        env:
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}

      - name: Build Agents
        run: ./build-agents stage

      - name: Deploy Agents
        uses: pulumi/actions@v6
        with:
          command: up
          stack-name: stage
          work-dir: ./agents
          cloud-url: s3://crc-pulumi/crc
        env:
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}